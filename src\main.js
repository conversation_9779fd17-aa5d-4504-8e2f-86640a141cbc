const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON>er<PERSON><PERSON><PERSON>, <PERSON>ray, Menu, ipc<PERSON>ain, screen, shell } = require('electron');
const path = require('path');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');
const PlaylistManager = require('./playlist-manager');
const AudioController = require('./audio-controller');

// Initialize store for persistent data
const store = new Store();

// Global references
let mainWindow = null;
let tray = null;
let youtubeView = null;
let spotifyView = null;
let isMinimized = false;
let isMuted = false;
let playlistManager = null;
let audioController = null;
let appInstance = null; // Singleton instance

// Auto-launch setup
const autoLauncher = new AutoLaunch({
  name: 'YSViewer',
  path: app.getPath('exe')
});

class YSViewerApp {
  constructor() {
    if (appInstance) {
      return appInstance;
    }
    appInstance = this;
    this.initializeApp();
  }

  static getInstance() {
    if (!appInstance) {
      appInstance = new YSViewerApp();
    }
    return appInstance;
  }

  async initializeApp() {
    // Initialize managers
    playlistManager = new PlaylistManager();
    audioController = new AudioController();

    // Setup auto-launch
    await this.setupAutoLaunch();

    // Create main window
    this.createMainWindow();

    // Create system tray
    this.createSystemTray();

    // Setup browser views
    this.setupBrowserViews();

    // Start playlist management
    await this.startPlaylistManagement();
  }

  async setupAutoLaunch() {
    try {
      const isEnabled = await autoLauncher.isEnabled();
      if (!isEnabled) {
        await autoLauncher.enable();
        console.log('Auto-launch enabled');
      }
    } catch (error) {
      console.error('Failed to setup auto-launch:', error);
    }
  }

  createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    mainWindow = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(800, height - 100),
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile(path.join(__dirname, 'electron-index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window close
    mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault();
        this.minimizeToTray();
      }
    });

    // Handle window minimize
    mainWindow.on('minimize', () => {
      this.minimizeToTray();
    });
  }

  createSystemTray() {
    // Create system tray - skip in development to avoid issues
    if (process.argv.includes('--dev')) {
      console.log('Skipping tray creation in development mode');
      return;
    }

    try {
      const { nativeImage } = require('electron');

      // Create a simple 16x16 green square icon
      const size = 16;
      const buffer = Buffer.alloc(size * size * 4);

      // Fill with green color
      for (let i = 0; i < buffer.length; i += 4) {
        buffer[i] = 76;     // R
        buffer[i + 1] = 175; // G
        buffer[i + 2] = 80;  // B
        buffer[i + 3] = 255; // A
      }

      const trayIcon = nativeImage.createFromBuffer(buffer, { width: size, height: size });
      tray = new Tray(trayIcon);
      console.log('System tray created successfully');
    } catch (error) {
      console.log('Tray creation failed, continuing without tray:', error.message);
      tray = null;
    }

    // Only create menu if tray was created successfully
    if (!tray) {
      console.log('Skipping tray menu creation - no tray available');
      return;
    }

    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'YSViewer',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: isMuted ? 'Unmute Application' : 'Mute Application',
        type: 'normal',
        click: () => this.toggleSystemAudio()
      },
      { type: 'separator' },
      {
        label: 'Show Window',
        type: 'normal',
        click: () => this.showMainWindow()
      },
      {
        label: 'Minimize to Dot',
        type: 'normal',
        click: () => this.minimizeToDot()
      },
      { type: 'separator' },
      {
        label: 'Quit',
        type: 'normal',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('YSViewer - YouTube & Spotify Player');

    // Handle tray click
    tray.on('click', () => {
      if (mainWindow.isVisible()) {
        this.minimizeToTray();
      } else {
        this.showMainWindow();
      }
    });
  }

  setupBrowserViews() {
    const bounds = mainWindow.getBounds();
    const viewWidth = Math.floor(bounds.width / 2);
    const viewHeight = bounds.height - 100; // Leave space for controls

    // YouTube view
    youtubeView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.setBrowserView(youtubeView);
    youtubeView.setBounds({ x: 0, y: 100, width: viewWidth, height: viewHeight });
    youtubeView.webContents.loadURL('https://www.youtube.com');

    // Register YouTube view for audio control
    audioController.registerWebContents(youtubeView.webContents);

    // Spotify view
    spotifyView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.addBrowserView(spotifyView);
    spotifyView.setBounds({ x: viewWidth, y: 100, width: viewWidth, height: viewHeight });

    // Register Spotify view for audio control
    audioController.registerWebContents(spotifyView.webContents);

    // Check if we have saved Spotify credentials
    this.initializeSpotifyLogin();

    // Handle window resize
    mainWindow.on('resize', () => {
      const newBounds = mainWindow.getBounds();
      const newViewWidth = Math.floor(newBounds.width / 2);
      const newViewHeight = newBounds.height - 100;

      youtubeView.setBounds({ x: 0, y: 100, width: newViewWidth, height: newViewHeight });
      spotifyView.setBounds({ x: newViewWidth, y: 100, width: newViewWidth, height: newViewHeight });
    });

    // Handle external links and auto-accept cookies
    [youtubeView, spotifyView].forEach((view, index) => {
      const platform = index === 0 ? 'YouTube' : 'Spotify';

      view.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });

      // Auto-accept cookies when page loads
      view.webContents.on('dom-ready', () => {
        const appInstance = YSViewerApp.getInstance();
        appInstance.autoAcceptCookies(view.webContents, platform);
      });

      // Also try after navigation
      view.webContents.on('did-finish-load', () => {
        setTimeout(() => {
          const appInstance = YSViewerApp.getInstance();
          appInstance.autoAcceptCookies(view.webContents, platform);
        }, 2000); // Wait 2 seconds for cookie banners to appear
      });
    });
  }

  async startPlaylistManagement() {
    try {
      await playlistManager.initialize();
      
      // Start playing immediately
      setTimeout(() => {
        this.playNextVideo();
      }, 3000); // Wait 3 seconds for views to load

      // Set up periodic playlist checking
      setInterval(() => {
        this.checkAndPlayNext();
      }, 3600000); // Check every hour
    } catch (error) {
      console.error('Failed to start playlist management:', error);
    }
  }

  async playNextVideo() {
    try {
      // Get next videos for both platforms
      const nextYouTubeVideo = await playlistManager.getNextMandatoryVideo('YouTube');
      const nextSpotifyVideo = await playlistManager.getNextMandatoryVideo('Spotify');

      // Play YouTube video if available
      if (nextYouTubeVideo && (nextYouTubeVideo.url.includes('youtube.com') || nextYouTubeVideo.url.includes('youtu.be'))) {
        console.log('Playing YouTube video:', nextYouTubeVideo.url);
        youtubeView.webContents.loadURL(nextYouTubeVideo.url);
      }

      // Play Spotify video if available
      if (nextSpotifyVideo && nextSpotifyVideo.url.includes('spotify.com')) {
        console.log('Playing Spotify track:', nextSpotifyVideo.url);
        spotifyView.webContents.loadURL(nextSpotifyVideo.url);
      }

      // If no platform-specific videos, try any available video
      if (!nextYouTubeVideo && !nextSpotifyVideo) {
        const anyVideo = await playlistManager.getNextMandatoryVideo();
        if (anyVideo) {
          console.log('Playing any available video:', anyVideo.url);

          if (anyVideo.url.includes('youtube.com') || anyVideo.url.includes('youtu.be')) {
            youtubeView.webContents.loadURL(anyVideo.url);
          } else if (anyVideo.url.includes('spotify.com')) {
            spotifyView.webContents.loadURL(anyVideo.url);
          }
        }
      }
    } catch (error) {
      console.error('Failed to play next video:', error);
    }
  }

  async checkAndPlayNext() {
    try {
      const shouldPlay = await playlistManager.shouldPlayNext();
      if (shouldPlay) {
        this.playNextVideo();
      }
    } catch (error) {
      console.error('Failed to check playlist:', error);
    }
  }

  async toggleSystemAudio() {
    try {
      isMuted = await audioController.toggleMute();
      this.updateTrayMenu();

      // Notify renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        try {
          mainWindow.webContents.send('audio-state-changed', { isMuted });
        } catch (sendError) {
          console.warn('Failed to send audio state to renderer:', sendError.message);
        }
      }

      return isMuted;
    } catch (error) {
      console.error('Failed to toggle audio:', error);
      throw new Error(`Audio toggle failed: ${error.message}`);
    }
  }

  updateTrayMenu() {
    if (tray && !tray.isDestroyed()) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'YSViewer',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: isMuted ? 'Unmute Application' : 'Mute Application',
          type: 'normal',
          click: () => this.toggleSystemAudio()
        },
        { type: 'separator' },
        {
          label: 'Show Window',
          type: 'normal',
          click: () => this.showMainWindow()
        },
        {
          label: 'Minimize to Dot',
          type: 'normal',
          click: () => this.minimizeToDot()
        },
        { type: 'separator' },
        {
          label: 'Quit',
          type: 'normal',
          click: () => {
            app.isQuiting = true;
            app.quit();
          }
        }
      ]);
      tray.setContextMenu(contextMenu);
    }
  }

  showMainWindow() {
    if (mainWindow) {
      if (isMinimized) {
        // Restore from minimized state
        mainWindow.setSize(1400, 800);
        mainWindow.center();
        isMinimized = false;
      }
      mainWindow.show();
      mainWindow.focus();
    }
  }

  minimizeToTray() {
    if (mainWindow) {
      mainWindow.hide();
    }
  }

  minimizeToDot() {
    if (mainWindow) {
      const { width, height } = screen.getPrimaryDisplay().workAreaSize;
      mainWindow.setSize(50, 50); // Changed from 5x5 to 50x50
      mainWindow.setPosition(width - 50, height - 50);
      mainWindow.setAlwaysOnTop(true);
      mainWindow.setSkipTaskbar(true);
      isMinimized = true;
    }
  }

  async initializeSpotifyLogin() {
    try {
      // Check if we have saved credentials
      const savedCredentials = store.get('spotifyCredentials');

      if (savedCredentials && savedCredentials.username && savedCredentials.password) {
        console.log('Found saved Spotify credentials, attempting auto-login');
        await this.loginToSpotify(savedCredentials.username, savedCredentials.password);
      } else {
        console.log('No saved Spotify credentials, showing login form');
        await this.showSpotifyLoginForm();
      }
    } catch (error) {
      console.error('Failed to initialize Spotify login:', error);
      await this.showSpotifyLoginForm();
    }
  }

  async showSpotifyLoginForm() {
    // Load a custom login page first
    const loginHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Spotify Login - YSViewer</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            background: #1db954;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
          }
          .login-container {
            background: rgba(0,0,0,0.8);
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
            width: 100%;
          }
          input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
          }
          button {
            background: #1db954;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
          }
          button:hover { background: #1ed760; }
          .skip-btn { background: #666; }
          .skip-btn:hover { background: #888; }
          h2 { margin-bottom: 20px; }
          .note { font-size: 12px; color: #ccc; margin-top: 15px; }
        </style>
      </head>
      <body>
        <div class="login-container">
          <h2>🎵 Spotify Login</h2>
          <p>Enter your Spotify credentials to enable automatic playback</p>
          <form id="loginForm">
            <input type="text" id="username" placeholder="Username or Email" required>
            <input type="password" id="password" placeholder="Password" required>
            <div>
              <button type="submit">Login</button>
              <button type="button" class="skip-btn" onclick="skipLogin()">Skip</button>
            </div>
          </form>
          <div class="note">
            Your credentials are stored locally and encrypted.<br>
            You can change them later in settings.
          </div>
        </div>
        <script>
          document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
              // Send credentials to main process
              window.electronAPI?.loginSpotify?.(username, password);
            }
          });

          function skipLogin() {
            window.electronAPI?.skipSpotifyLogin?.();
          }
        </script>
      </body>
      </html>
    `;

    // Create a data URL for the login page
    const dataURL = 'data:text/html;charset=utf-8,' + encodeURIComponent(loginHTML);
    spotifyView.webContents.loadURL(dataURL);

    // Set up IPC for login
    spotifyView.webContents.on('dom-ready', () => {
      spotifyView.webContents.executeJavaScript(`
        window.electronAPI = {
          loginSpotify: (username, password) => {
            console.log('Login attempt with username:', username);
            // This will be handled by the main process
          },
          skipSpotifyLogin: () => {
            console.log('Skipping Spotify login');
            // Load Spotify without login
          }
        };
      `);
    });
  }

  async loginToSpotify(username, password) {
    try {
      console.log('Attempting Spotify login for user:', username);

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load and then fill in credentials
      spotifyView.webContents.once('did-finish-load', async () => {
        setTimeout(async () => {
          try {
            await spotifyView.webContents.executeJavaScript(`
              (function() {
                // Fill in username
                const usernameField = document.querySelector('#login-username') ||
                                    document.querySelector('input[name="username"]') ||
                                    document.querySelector('input[type="text"]') ||
                                    document.querySelector('input[type="email"]');

                if (usernameField) {
                  usernameField.value = '${username}';
                  usernameField.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // Fill in password
                const passwordField = document.querySelector('#login-password') ||
                                    document.querySelector('input[name="password"]') ||
                                    document.querySelector('input[type="password"]');

                if (passwordField) {
                  passwordField.value = '${password}';
                  passwordField.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // Submit form
                setTimeout(() => {
                  const loginButton = document.querySelector('#login-button') ||
                                    document.querySelector('button[type="submit"]') ||
                                    document.querySelector('button:contains("Log In")') ||
                                    document.querySelector('.btn-green');

                  if (loginButton) {
                    loginButton.click();
                  }
                }, 500);

                return true;
              })();
            `);

            // Save credentials if login appears successful
            store.set('spotifyCredentials', { username, password });
            console.log('Spotify credentials saved');

          } catch (error) {
            console.error('Failed to auto-fill Spotify login:', error);
          }
        }, 2000);
      });

    } catch (error) {
      console.error('Failed to login to Spotify:', error);
      throw error;
    }
  }

  async autoAcceptCookies(webContents, platform) {
    try {
      console.log(`Attempting to auto-accept cookies for ${platform}`);

      if (platform === 'YouTube') {
        // YouTube cookie acceptance
        await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for YouTube cookie consent
            const selectors = [
              'button[aria-label*="Accept"]',
              'button[aria-label*="accept"]',
              'button:contains("Accept all")',
              'button:contains("I agree")',
              'button:contains("Accept")',
              '[data-testid="accept-button"]',
              '.VfPpkd-LgbsSe[jsname="tWT92d"]', // YouTube's accept button
              'button[jsname="tWT92d"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null) {
                console.log('Found YouTube cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content
            const buttons = document.querySelectorAll('button');
            for (let button of buttons) {
              const text = button.textContent.toLowerCase();
              if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                console.log('Found YouTube cookie button by text:', text);
                button.click();
                return true;
              }
            }

            return false;
          })();
        `);
      } else if (platform === 'Spotify') {
        // Spotify cookie acceptance
        await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for Spotify cookie consent
            const selectors = [
              'button[data-testid="accept-all-cookies"]',
              'button[id*="accept"]',
              'button:contains("Accept all")',
              'button:contains("Accept cookies")',
              'button:contains("I agree")',
              'button:contains("Accept")',
              '.onetrust-accept-btn-handler',
              '#onetrust-accept-btn-handler',
              '[data-cy="accept-all-cookies"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null) {
                console.log('Found Spotify cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content
            const buttons = document.querySelectorAll('button');
            for (let button of buttons) {
              const text = button.textContent.toLowerCase();
              if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                console.log('Found Spotify cookie button by text:', text);
                button.click();
                return true;
              }
            }

            return false;
          })();
        `);
      }
    } catch (error) {
      console.log(`Failed to auto-accept cookies for ${platform}:`, error.message);
    }
  }
}

// App event handlers
app.whenReady().then(() => {
  YSViewerApp.getInstance();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    YSViewerApp.getInstance().showMainWindow();
  }
});

// IPC handlers
ipcMain.handle('get-app-state', () => {
  return {
    isMuted,
    isMinimized
  };
});

ipcMain.handle('toggle-audio', async () => {
  try {
    const app = YSViewerApp.getInstance();
    await app.toggleSystemAudio();
    return { success: true, isMuted };
  } catch (error) {
    console.error('IPC toggle-audio error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('minimize-to-dot', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToDot();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-dot error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-window', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.showMainWindow();
    return { success: true };
  } catch (error) {
    console.error('IPC show-window error:', error);
    return { success: false, error: error.message };
  }
});
